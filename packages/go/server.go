package webshell

import (
	"context"

	"github.com/gorilla/websocket"
	"github.com/sourcegraph/jsonrpc2"
)

type WebshellServer struct {
	WS  *websocket.Conn
	RPC *jsonrpc2.Conn
	*ServiceRegistry
}

func NewWebshellServer(ws *websocket.Conn, services ...Service) *WebshellServer {
	ctx := context.Background()

	sr := NewServiceRegistry()

	for _, s := range services {
		sr.Register(s)
	}

	stream := &websocketObjectStream{conn: ws}

	rpc := jsonrpc2.NewConn(ctx, stream, sr)

	return &WebshellServer{
		WS:              ws,
		RPC:             rpc,
		ServiceRegistry: sr,
	}
}

type websocketObjectStream struct {
	conn *websocket.Conn
}

func (stream *websocketObjectStream) WriteObject(obj any) error {
	return stream.conn.WriteJSON(obj)
}

func (stream *websocketObjectStream) ReadObject(v any) error {
	return stream.conn.ReadJSON(v)
}

func (stream *websocketObjectStream) Close() error {
	return stream.conn.Close()
}
