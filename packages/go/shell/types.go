package shell

type StartParams struct {
	Shellid string `json:"shellid"`
	Cwd     string `json:"cwd,omitempty"`
}

type CommandServerParams struct {
	Shellid string `json:"shellid"`
	Command string `json:"command"`
}

type CommandClientParams struct {
	Shellid string `json:"shellid"`
	Output  string `json:"output"`
}

type ResizeParams struct {
	Shellid string `json:"shellid"`
	Cols    int    `json:"cols"`
	Rows    int    `json:"rows"`
}

type TerminateParams struct {
	Shellid string `json:"shellid"`
}
