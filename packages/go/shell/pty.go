package shell

import (
	"context"
	"encoding/json"
	"os"

	"github.com/sourcegraph/jsonrpc2"
)

type PTYService struct {
	ptys map[string]*os.File
}

func (s *PTYService) Name() string {
	return "shell"
}

func (s *PTYService) HandleMethod(ctx context.Context, method string, params *json.RawMessage) (any, *jsonrpc2.Error) {
	switch method {
	case "start":
		return s.start(ctx, params)
	case "command":
		return s.command(ctx, params)
	case "resize":
		return s.resize(ctx, params)
	case "terminate":
		return s.terminate(ctx, params)
	default:
		return nil, &jsonrpc2.Error{Code: jsonrpc2.CodeMethodNotFound, Message: "Method not found"}
	}
}

func (s *PTYService) start(ctx context.Context, params *json.RawMessage) (any, *jsonrpc2.Error) {
	return nil, nil
}

func (s *PTYService) command(ctx context.Context, params *json.RawMessage) (any, *jsonrpc2.Error) {
	return nil, nil
}

func (s *PTYService) resize(ctx context.Context, params *json.RawMessage) (any, *jsonrpc2.Error) {
	return nil, nil
}

func (s *PTYService) terminate(ctx context.Context, params *json.RawMessage) (any, *jsonrpc2.Error) {
	return nil, nil
}
