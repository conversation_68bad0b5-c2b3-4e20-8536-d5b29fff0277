package webshell

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/sourcegraph/jsonrpc2"
)

type Service interface {
	Name() string
	HandleMethod(ctx context.Context, method string, params *json.RawMessage) (any, *jsonrpc2.Error)
}

type ServiceRegistry struct {
	Services map[string]Service
}

func NewServiceRegistry() *ServiceRegistry {
	return &ServiceRegistry{
		Services: make(map[string]Service),
	}
}

func (sr *ServiceRegistry) Register(s Service) error {
	_, exists := sr.Services[s.Name()]
	if exists {
		return fmt.Errorf("Service %s already registered", s.Name())
	}
	sr.Services[s.Name()] = s
	return nil
}

func (sr *ServiceRegistry) Unregister(s Service) {
	delete(sr.Services, s.Name())
}

func (sr *ServiceRegistry) Handle(ctx context.Context, c *jsonrpc2.Conn, r *jsonrpc2.Request) {
	service, method := func() (string, string) {
		parts := strings.Split(r.Method, ".")
		if len(parts) != 2 {
			return "", ""
		}
		return parts[0], parts[1]
	}()

	if service == "" || method == "" {
		err := &jsonrpc2.Error{Code: jsonrpc2.CodeMethodNotFound, Message: "Method not found"}
		c.ReplyWithError(ctx, r.ID, err)
		return
	}

	s, exists := sr.Services[service]
	if !exists {
		err := &jsonrpc2.Error{Code: jsonrpc2.CodeMethodNotFound, Message: "Method not found"}
		c.ReplyWithError(ctx, r.ID, err)
		return
	}

	result, err := s.HandleMethod(ctx, method, r.Params)
	if err != nil {
		c.ReplyWithError(ctx, r.ID, err)
		return
	}
	c.Reply(ctx, r.ID, result)
}
