export type MethodsType = Record<string, (params?: any) => any>

export type GetParamsType<TMethods extends MethodsType, <PERSON><PERSON><PERSON><PERSON> extends keyof TMethods> = TMethods[TMethod] extends (
  params: infer TParams
) => any
  ? TParams
  : never

export type GetResultType<TMethods extends MethodsType, <PERSON><PERSON><PERSON><PERSON> extends keyof TMethods> = TMethods[TMethod] extends (
  params: any
) => infer TResult
  ? TResult
  : never

// 为 notify 添加类型
declare module 'json-rpc-2.0' {
  export interface TypedJSONRPCServerAndClient<
    ServerMethods extends MethodsType,
    ClientMethods extends MethodsType,
    ServerParams = void,
    ClientParams = void
  > {
    notify<Method extends Extract<keyof ClientMethods, string>>(
      method: Method,
      ...args: Parameters<ClientMethods[Method]>[0] extends undefined
        ? [void, ClientParams]
        : [Parameters<ClientMethods[Method]>[0], ClientParams]
    ): void
  }
}
