import { JSONRPCClient, JSONRPCServer, JSONRPCServerAndClient, type TypedJSONRPCServerAndClient } from 'json-rpc-2.0'

import type { MethodsType } from './shared'
export type { GetParamsType, GetResultType } from './shared'

export class WebshellClient<ServerMethods extends MethodsType = {}, ClientMethods extends MethodsType = {}> {
  readonly rpc: TypedJSONRPCServerAndClient<ClientMethods, ServerMethods>

  constructor(readonly ws: WebSocket) {
    this.rpc = new JSONRPCServerAndClient(
      new JSONRPCServer(),
      new JSONRPCClient((request) => ws.send(JSON.stringify(request)))
    )

    ws.addEventListener('message', (event) => {
      if (typeof event.data === 'string') {
        this.rpc.receiveAndSend(JSON.parse(event.data))
      }
    })

    ws.addEventListener('close', (event) => {
      this.rpc.rejectAllPendingRequests(`Websocket connection is closed: ${event.reason}`)
    })
  }
}
