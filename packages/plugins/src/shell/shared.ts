export type ShellServerMethods = {
  'shell.command': (params: { shellid: string; command: string }) => void
  'shell.resize': (params: { shellid: string; cols: number; rows: number }) => void
  'shell.start': (params: { shellid: string; cwd?: string }) => void
  'shell.terminate': (params: { shellid: string }) => void
}

export type ShellClientMethods = {
  'shell.command': (params: { shellid: string; output: string }) => void
}
