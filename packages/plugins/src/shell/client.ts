import type { Terminal } from '@xterm/xterm'
import EventEmitter from 'eventemitter3'

import type { WebshellClient, GetParamsType } from '@webshell/core/client'

import type { ShellServerMethods, ShellClientMethods } from './shared'

export enum ShellEvents {
  Started = 'started',
}

export type ShellStartedEvent = {
  shellid: string
}

export class WebshellShellClient {
  readonly terminals = new Map<string, Terminal>()
  readonly eventEmitter = new EventEmitter()

  constructor(readonly webshell: WebshellClient<ShellServerMethods, ShellClientMethods>) {
    webshell.rpc.addMethod('shell.command', (params) => this.handleCommandOutput(params))
  }

  addTerminal(shellid: string, terminal: Terminal) {
    this.terminals.set(shellid, terminal)

    const terminalDisposables = [
      terminal.onData((command) => this.webshell.rpc.notify('shell.command', { shellid, command })),
      terminal.onResize(({ cols, rows }) => this.webshell.rpc.request('shell.resize', { shellid, cols, rows })),
    ]

    const startShell = async () => {
      await this.webshell.rpc.request('shell.start', { shellid })
      this.eventEmitter.emit(ShellEvents.Started, <ShellStartedEvent>{ shellid })
    }

    if (this.webshell.ws.readyState === WebSocket.OPEN) {
      startShell()
    } else if (this.webshell.ws.readyState === WebSocket.CONNECTING) {
      this.webshell.ws.addEventListener('open', startShell)
    } else {
      throw new Error('websocket is neither open nor connecting.')
    }

    return terminalDisposables
  }

  removeTerminal(shellid: string) {
    const terminal = this.terminals.get(shellid)
    if (!terminal) {
      return
    }

    terminal.dispose()
    this.terminals.delete(shellid)
    this.webshell.rpc.request('shell.terminate', { shellid })
  }

  private handleCommandOutput({ shellid, output }: GetParamsType<ShellClientMethods, 'shell.command'>) {
    const terminal = this.terminals.get(shellid)
    if (terminal) {
      terminal.write(output)
    }
  }
}
